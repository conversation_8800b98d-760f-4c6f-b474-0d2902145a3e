/* 基础样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    padding-top: 56px; /* 导航栏高度 */
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    background-color: #2c3e50 !important;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    transition: transform 0.3s ease;
    width: 16.66667%; /* col-lg-2 width */
}

/* 侧边栏收起状态 */
.sidebar.collapsed {
    transform: translateX(-100%);
}

/* 侧边栏切换按钮样式 */
#sidebarToggle {
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

#sidebarToggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.sidebar .nav-link {
    color: #ecf0f1;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.sidebar .nav-link:hover {
    background-color: #34495e;
    color: #3498db;
}

.sidebar .nav-link.active {
    background-color: #3498db;
    color: white;
}

.sidebar .nav-link i {
    width: 16px;
    text-align: center;
}

.sidebar .nav-link i.me-2 {
    margin-right: 0.5rem !important;
}

.sidebar .nav-link i.ms-auto {
    margin-left: auto !important;
    width: auto;
}

/* 下拉箭头样式调整 - 移至右侧 */
.sidebar .nav-link[data-bs-toggle="collapse"] {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar .nav-link[data-bs-toggle="collapse"] .fa-chevron-down {
    transition: transform 0.3s ease;
    margin-left: auto !important;
}

.sidebar .nav-link[data-bs-toggle="collapse"]:not(.collapsed) .fa-chevron-down {
    transform: rotate(180deg);
}

/* 子菜单样式 */
.sidebar .collapse .nav-link {
    color: #bdc3c7;
    background-color: rgba(0, 0, 0, 0.1);
    border-left: 3px solid transparent;
}

.sidebar .collapse .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.2);
    color: #3498db;
    border-left-color: #3498db;
}

/* 主内容区域 */
main {
    padding-top: 20px;
    min-height: calc(100vh - 56px);
    background-color: #ffffff;
    transition: margin-left 0.3s ease, width 0.3s ease;
}

/* ========== 标签页样式 ========== */

/* 标签页容器 */
.tab-container {
    min-height: calc(100vh - 76px);
    display: flex;
    flex-direction: column;
}

/* 标签页导航包装器 */
.tab-nav-wrapper {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    scrollbar-width: thin;
    scrollbar-color: #6c757d #f8f9fa;
}

.tab-nav-wrapper::-webkit-scrollbar {
    height: 4px;
}

.tab-nav-wrapper::-webkit-scrollbar-track {
    background: #f8f9fa;
}

.tab-nav-wrapper::-webkit-scrollbar-thumb {
    background: #6c757d;
    border-radius: 2px;
}

.tab-nav-wrapper::-webkit-scrollbar-thumb:hover {
    background: #495057;
}

/* 标签页导航 */
.tab-nav {
    display: flex;
    align-items: center;
    min-height: 42px;
    padding: 0 15px;
    gap: 2px;
}

/* 标签页项 */
.tab-item {
    display: inline-flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #e9ecef;
    border: 1px solid #dee2e6;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    color: #495057;
    text-decoration: none;
    font-size: 14px;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.2s ease;
    max-width: 200px;
    min-width: 120px;
    position: relative;
}

.tab-item:hover {
    background-color: #dee2e6;
    color: #212529;
}

.tab-item.active {
    background-color: #ffffff;
    color: #0d6efd;
    border-color: #0d6efd;
    border-bottom: 1px solid #ffffff;
    margin-bottom: -1px;
    z-index: 1;
}

/* 标签页标题 */
.tab-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px;
}

/* 标签页图标 */
.tab-icon {
    margin-right: 6px;
    font-size: 12px;
}

/* 标签页关闭按钮 */
.tab-close {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    color: #6c757d;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.tab-close:hover {
    background-color: #dc3545;
    color: #ffffff;
}

.tab-item.non-closable .tab-close {
    display: none;
}

/* 标签页内容包装器 */
.tab-content-wrapper {
    flex: 1;
    overflow-y: auto;
    background-color: #ffffff;
    padding: 0;
    position: relative;
}

/* 标签页内容 */
.tab-content {
    display: none;
    height: 100%;
    padding: 20px;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

/* 确保标签页内容中的表格和图表正确显示 */
.tab-content .table-responsive {
    margin-bottom: 1rem;
}

.tab-content .card {
    margin-bottom: 1rem;
}

/* 修复可能的样式冲突 */
.tab-content .container-fluid {
    padding: 0;
}

.tab-content .row {
    margin: 0;
}

.tab-content .col-12,
.tab-content .col-md-12 {
    padding: 0;
}

/* 加载状态 */
.tab-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
}

.tab-loading .spinner-border {
    margin-right: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tab-item {
        min-width: 100px;
        max-width: 150px;
        padding: 6px 10px;
        font-size: 13px;
    }

    .tab-title {
        margin-right: 6px;
    }

    .tab-close {
        width: 14px;
        height: 14px;
        font-size: 10px;
    }

    .tab-content-wrapper {
        padding: 15px;
    }
}

/* 侧边栏收起时主内容区域调整 */
.sidebar-collapsed main {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* 确保内容区域平滑过渡 */
.container-fluid .row {
    transition: all 0.3s ease;
}

/* 侧边栏收起时调整容器 */
.sidebar-collapsed .container-fluid .row {
    margin-left: 0;
}

/* 卡片样式 */
.card {
    border: 1px solid #e9ecef;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
    border-radius: 0.5rem;
}

.card:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #e9ecef;
    color: #495057;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.075);
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}
/* 固定顶部导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
}
/* 欢迎页面样式 */
.welcome-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    margin: 2rem 0;
}

.welcome-section .card {
    transition: transform 0.3s ease;
}

.welcome-section .card:hover {
    transform: translateY(-5px);
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .sidebar {
        position: relative;
        top: 0;
        height: auto;
        width: 100%;
        transform: none;
    }

    .sidebar.collapsed {
        display: none;
    }

    main {
        padding-top: 10px;
    }

    .sidebar-collapsed main {
        margin-left: 0;
        width: 100%;
    }
}

@media (min-width: 768px) {
    .sidebar {
        width: 25%; /* col-md-3 width */
    }
}

@media (min-width: 992px) {
    .sidebar {
        width: 16.66667%; /* col-lg-2 width */
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 消息提示样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1055;
}

/* 表单样式优化 */
.form-label {
    font-weight: 500;
    color: #495057;
}

.form-control:focus,
.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 徽章样式 */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}
