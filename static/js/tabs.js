/**
 * 标签页管理系统
 */

class TabManager {
    constructor() {
        this.tabs = new Map();
        this.activeTabId = null;
        this.tabCounter = 0;
        this.tabNav = document.getElementById('tabNav');
        this.tabContentWrapper = document.getElementById('tabContentWrapper');
        
        // 初始化
        this.init();
    }

    /**
     * 初始化标签页管理器
     */
    init() {
        console.log('开始初始化标签页管理器');

        // 检查必要的DOM元素
        if (!this.tabNav || !this.tabContentWrapper) {
            console.error('标签页容器元素未找到');
            return;
        }

        // 创建首页标签页
        this.createHomeTab();

        // 绑定侧边栏点击事件
        this.bindSidebarEvents();

        // 绑定键盘快捷键
        this.bindKeyboardShortcuts();

        console.log('标签页管理器初始化完成');
    }

    /**
     * 创建首页标签页
     */
    createHomeTab() {
        console.log('创建首页标签页');

        // 获取原始页面内容（在标签页容器之外的内容）
        const originalContent = this.getOriginalPageContent();
        console.log('获取到的原始内容长度:', originalContent.length);

        const homeTab = {
            id: 'home',
            title: '首页',
            url: '/',
            icon: 'fas fa-home',
            isClosable: false,
            content: originalContent
        };

        this.addTab(homeTab);
        this.setActiveTab('home');
        console.log('首页标签页创建完成');
    }

    /**
     * 获取原始页面内容
     */
    getOriginalPageContent() {
        console.log('获取原始页面内容');

        // 如果页面有 block content，尝试获取
        const contentBlocks = document.querySelectorAll('[data-original-content]');
        console.log('找到原始内容块数量:', contentBlocks.length);

        if (contentBlocks.length > 0) {
            const content = contentBlocks[0].innerHTML;
            console.log('原始内容长度:', content.length);
            console.log('原始内容预览:', content.substring(0, 200));
            return content;
        }

        // 否则返回默认首页内容
        console.log('使用默认首页内容');
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">欢迎使用NVH数据管理系统</h1>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="welcome-section text-center py-5">
                        <h2 class="mb-4">欢迎使用NVH数据管理系统</h2>
                        <p class="lead text-muted mb-4">请从左侧菜单选择功能模块进行操作</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成唯一标签页ID
     */
    generateTabId() {
        return `tab-${++this.tabCounter}-${Date.now()}`;
    }

    /**
     * 添加新标签页
     */
    addTab(tabData) {
        const tabId = tabData.id || this.generateTabId();
        console.log('添加标签页:', tabId, tabData.title);

        // 检查标签页是否已存在
        if (this.tabs.has(tabId)) {
            console.log('标签页已存在，切换到:', tabId);
            this.setActiveTab(tabId);
            return tabId;
        }

        // 创建标签页数据
        const tab = {
            id: tabId,
            title: tabData.title || '新标签页',
            url: tabData.url || '#',
            icon: tabData.icon || 'fas fa-file',
            isClosable: tabData.isClosable !== false,
            content: tabData.content || '',
            isLoaded: !!tabData.content
        };

        this.tabs.set(tabId, tab);
        console.log('渲染标签页:', tabId);
        this.renderTab(tab);
        this.renderTabContent(tab);

        console.log('标签页添加完成:', tabId);
        return tabId;
    }

    /**
     * 渲染标签页
     */
    renderTab(tab) {
        const tabElement = document.createElement('div');
        tabElement.className = 'tab-item';
        tabElement.dataset.tabId = tab.id;
        if (!tab.isClosable) {
            tabElement.classList.add('non-closable');
        }

        tabElement.innerHTML = `
            <i class="tab-icon ${tab.icon}"></i>
            <span class="tab-title" title="${tab.title}">${tab.title}</span>
            ${tab.isClosable ? '<button class="tab-close" title="关闭标签页"><i class="fas fa-times"></i></button>' : ''}
        `;

        // 绑定点击事件
        tabElement.addEventListener('click', (e) => {
            if (e.target.closest('.tab-close')) {
                this.closeTab(tab.id);
            } else {
                this.setActiveTab(tab.id);
            }
        });

        this.tabNav.appendChild(tabElement);
    }

    /**
     * 渲染标签页内容
     */
    renderTabContent(tab) {
        const contentElement = document.createElement('div');
        contentElement.className = 'tab-content';
        contentElement.dataset.tabId = tab.id;
        contentElement.innerHTML = tab.content;

        this.tabContentWrapper.appendChild(contentElement);
    }

    /**
     * 设置活动标签页
     */
    setActiveTab(tabId) {
        if (!this.tabs.has(tabId)) {
            return;
        }

        // 移除所有活动状态
        document.querySelectorAll('.tab-item.active').forEach(el => {
            el.classList.remove('active');
        });
        document.querySelectorAll('.tab-content.active').forEach(el => {
            el.classList.remove('active');
        });

        // 设置新的活动状态
        const tabElement = document.querySelector(`[data-tab-id="${tabId}"]`);
        const contentElement = document.querySelector(`.tab-content[data-tab-id="${tabId}"]`);
        
        if (tabElement) tabElement.classList.add('active');
        if (contentElement) contentElement.classList.add('active');

        this.activeTabId = tabId;
        
        // 如果内容未加载，则加载内容
        const tab = this.tabs.get(tabId);
        if (!tab.isLoaded && tab.url !== '#') {
            this.loadTabContent(tabId);
        }

        // 滚动到活动标签页
        this.scrollToActiveTab();
    }

    /**
     * 关闭标签页
     */
    closeTab(tabId) {
        if (!this.tabs.has(tabId)) {
            return;
        }

        const tab = this.tabs.get(tabId);
        
        // 不可关闭的标签页
        if (!tab.isClosable) {
            return;
        }

        // 至少保留一个标签页
        if (this.tabs.size <= 1) {
            return;
        }

        // 移除DOM元素
        const tabElement = document.querySelector(`[data-tab-id="${tabId}"]`);
        const contentElement = document.querySelector(`.tab-content[data-tab-id="${tabId}"]`);
        
        if (tabElement) tabElement.remove();
        if (contentElement) contentElement.remove();

        // 如果关闭的是活动标签页，切换到其他标签页
        if (this.activeTabId === tabId) {
            const remainingTabs = Array.from(this.tabs.keys()).filter(id => id !== tabId);
            if (remainingTabs.length > 0) {
                this.setActiveTab(remainingTabs[0]);
            }
        }

        // 从Map中删除
        this.tabs.delete(tabId);
    }

    /**
     * 加载标签页内容
     */
    async loadTabContent(tabId) {
        const tab = this.tabs.get(tabId);
        if (!tab) return;

        const contentElement = document.querySelector(`.tab-content[data-tab-id="${tabId}"]`);
        if (!contentElement) return;

        // 显示加载状态
        contentElement.innerHTML = `
            <div class="tab-loading">
                <div class="spinner-border spinner-border-sm" role="status"></div>
                <span>加载中...</span>
            </div>
        `;

        try {
            const response = await fetch(tab.url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const html = await response.text();
            
            // 提取页面主要内容
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // 尝试多种方式提取内容
            let content = null;

            // 1. 查找原始内容标记
            content = doc.querySelector('[data-original-content]');

            // 2. 查找主内容区域
            if (!content) {
                content = doc.querySelector('main .tab-content-wrapper') ||
                         doc.querySelector('main') ||
                         doc.querySelector('.container-fluid') ||
                         doc.querySelector('.container');
            }

            // 3. 最后尝试body
            if (!content) {
                content = doc.body;
            }

            if (content) {
                let extractedContent = content.innerHTML;

                // 如果是完整页面，需要清理不需要的部分
                if (content === doc.body) {
                    // 移除导航栏、侧边栏等
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = extractedContent;

                    // 移除不需要的元素
                    const elementsToRemove = tempDiv.querySelectorAll('nav, .sidebar, script, .tab-container');
                    elementsToRemove.forEach(el => el.remove());

                    // 查找主要内容
                    const mainContent = tempDiv.querySelector('main') || tempDiv;
                    extractedContent = mainContent.innerHTML;
                }

                contentElement.innerHTML = extractedContent;
                tab.content = extractedContent;
                tab.isLoaded = true;

                // 重新初始化页面脚本
                this.reinitializeScripts(contentElement);
            } else {
                throw new Error('无法提取页面内容');
            }
        } catch (error) {
            console.error('加载标签页内容失败:', error);
            contentElement.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    加载失败: ${error.message}
                    <button class="btn btn-sm btn-outline-danger ms-2" onclick="tabManager.loadTabContent('${tabId}')">
                        重试
                    </button>
                </div>
            `;
        }
    }

    /**
     * 重新初始化脚本
     */
    reinitializeScripts(container) {
        // 重新执行页面特定的初始化脚本
        const scripts = container.querySelectorAll('script');
        scripts.forEach(script => {
            if (script.src) {
                // 外部脚本
                const newScript = document.createElement('script');
                newScript.src = script.src;
                document.head.appendChild(newScript);
            } else if (script.textContent) {
                // 内联脚本
                try {
                    eval(script.textContent);
                } catch (error) {
                    console.warn('脚本执行失败:', error);
                }
            }
        });

        // 触发自定义事件，通知页面内容已加载
        const event = new CustomEvent('tabContentLoaded', {
            detail: { container }
        });
        document.dispatchEvent(event);
    }

    /**
     * 滚动到活动标签页
     */
    scrollToActiveTab() {
        const activeTab = document.querySelector('.tab-item.active');
        if (activeTab) {
            activeTab.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'center'
            });
        }
    }

    /**
     * 绑定侧边栏事件
     */
    bindSidebarEvents() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) {
            console.warn('侧边栏元素未找到');
            return;
        }

        sidebar.addEventListener('click', (e) => {
            const link = e.target.closest('a.nav-link');
            if (!link || !link.href || link.href === '#') return;

            // 排除折叠菜单链接
            if (link.hasAttribute('data-bs-toggle')) return;

            e.preventDefault();

            // 提取页面信息
            const url = new URL(link.href);
            const pathname = url.pathname;
            const title = link.textContent.trim();
            const icon = link.querySelector('i')?.className || 'fas fa-file';

            console.log('点击侧边栏链接:', { pathname, title, icon });

            // 检查是否已有相同URL的标签页
            const existingTabId = this.getTabIdByUrl(pathname);

            if (existingTabId) {
                console.log('切换到已存在的标签页:', existingTabId);
                this.setActiveTab(existingTabId);
            } else {
                console.log('创建新标签页');
                const newTabId = this.addTab({
                    title: title,
                    url: pathname,
                    icon: icon,
                    isClosable: true
                });
                this.setActiveTab(newTabId);
            }
        });
    }

    /**
     * 根据URL获取标签页ID
     */
    getTabIdByUrl(url) {
        for (const [tabId, tab] of this.tabs) {
            if (tab.url === url) {
                return tabId;
            }
        }
        return null;
    }

    /**
     * 绑定键盘快捷键
     */
    bindKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+W 关闭当前标签页
            if ((e.ctrlKey || e.metaKey) && e.key === 'w') {
                e.preventDefault();
                if (this.activeTabId) {
                    this.closeTab(this.activeTabId);
                }
            }
            
            // Ctrl+Tab 切换到下一个标签页
            if ((e.ctrlKey || e.metaKey) && e.key === 'Tab') {
                e.preventDefault();
                this.switchToNextTab();
            }
        });
    }

    /**
     * 切换到下一个标签页
     */
    switchToNextTab() {
        const tabIds = Array.from(this.tabs.keys());
        const currentIndex = tabIds.indexOf(this.activeTabId);
        const nextIndex = (currentIndex + 1) % tabIds.length;
        this.setActiveTab(tabIds[nextIndex]);
    }
}

// 全局标签页管理器实例
let tabManager = null;

// 页面加载完成后初始化标签页管理器
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，开始检查标签页容器');

    // 只在有标签页容器的页面初始化
    const tabContainer = document.getElementById('tabContainer');
    console.log('标签页容器存在:', !!tabContainer);

    if (tabContainer) {
        try {
            console.log('开始初始化标签页管理器');
            tabManager = new TabManager();
            console.log('标签页管理器初始化成功');
        } catch (error) {
            console.error('标签页管理器初始化失败:', error);
        }
    } else {
        console.log('未找到标签页容器，跳过初始化');
    }
});

// 备用初始化方式
window.addEventListener('load', function() {
    if (!tabManager && document.getElementById('tabContainer')) {
        console.log('使用备用方式初始化标签页管理器');
        try {
            tabManager = new TabManager();
            console.log('备用初始化成功');
        } catch (error) {
            console.error('备用初始化失败:', error);
        }
    }
});
