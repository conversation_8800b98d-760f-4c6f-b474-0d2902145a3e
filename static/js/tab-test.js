/**
 * 标签页功能测试脚本
 */

// 简单的调试信息显示
function showDebugInfo() {
    // 在页面顶部添加调试信息
    const debugDiv = document.createElement('div');
    debugDiv.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; background: red; color: white; padding: 10px; z-index: 9999; font-size: 14px;';
    debugDiv.innerHTML = '标签页测试脚本已加载 - ' + new Date().toLocaleTimeString();
    document.body.appendChild(debugDiv);

    // 3秒后移除
    setTimeout(() => {
        debugDiv.remove();
    }, 3000);
}

// 页面加载完成后执行测试
document.addEventListener('DOMContentLoaded', function() {
    showDebugInfo();

    // 检查标签页容器
    const tabContainer = document.getElementById('tabContainer');
    if (tabContainer) {
        alert('找到标签页容器！');
    } else {
        alert('未找到标签页容器！');
    }
});
